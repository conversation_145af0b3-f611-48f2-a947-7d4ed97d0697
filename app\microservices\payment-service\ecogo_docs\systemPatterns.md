# System Patterns

## Current Database Architecture
- SQLAlchemy ORM pattern
- Declarative base for models
- Session-per-request pattern
- SQLite database file (test.db)

## Planned PostgreSQL Changes
- Replace SQLite with PostgreSQL
- Use connection pooling
- Configure for production workloads
- Add proper transaction isolation

## Migration Strategy
- Alembic for schema migrations
- Version-controlled database changes
- Support for rollbacks
- Environment-specific migrations

## Settings Management
- Move to Pydantic settings
- Environment variable support
- Type-safe configuration
- Hierarchical settings structure
