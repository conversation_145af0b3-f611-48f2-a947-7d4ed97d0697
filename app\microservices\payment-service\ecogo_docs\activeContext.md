# Active Context

## Current Task
Migrate database from SQLite to PostgreSQL and add:
- Alembic for database migrations
- Pydantic for settings management

## Current State
- Using SQLite with SQLAlchemy
- Single database.py file with all DB config
- No migration system
- Settings in config/settings.py

## Planned Changes
1. Add PostgreSQL dependencies
2. Configure Alembic for migrations
3. Implement Pydantic settings
4. Update database connection logic
5. Update Docker setup with PostgreSQL service

## Next Steps
1. Analyze current requirements.txt
2. Examine current settings.py
3. Review docker-compose.yml
4. Implement changes step by step
