from fastapi import <PERSON><PERSON><PERSON>

from app.middleware.jwt_auth_middleware import JWTValidationMiddleware

from app.routers.payment import router as payment_router
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Inventory Service")

app.add_middleware(JWTValidationMiddleware)
# Configure CORS
origins = [
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:8000",
    "https://fareosms.com",
    "https://*.fareosms.com",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Include API routers
app.include_router(payment_router, prefix="/api/v1", tags=["payment"])

# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)
