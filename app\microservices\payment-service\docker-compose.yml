version: "3.8"

services:
  payment_service:
    build: .
    ports:
      - "8004:8000"
    volumes:
      - .:/app
    environment:
      - DB_URL=************************************/payment_db
      - DB_POOL_SIZE=10
      - DB_MAX_OVERFLOW=20
      - DB_POOL_TIMEOUT=30
      - APP_ENVIRONMENT=development
      - APP_LOG_LEVEL=INFO
    depends_on:
      - db

  db:
    image: postgres:14
    restart: always
    environment:
      POSTGRES_DB: payment_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secret
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

volumes:
  db_data:
