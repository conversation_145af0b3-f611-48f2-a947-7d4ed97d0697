from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>MP, <PERSON><PERSON><PERSON>, Column, text
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class BaseModelMixin:
    """Mixin providing common fields for all models."""
    created_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text("now()"))
    updated_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text("now()"), 
                         onupdate=text("now()"))
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    deleted_at = Column(TIMESTAMP(timezone=True), nullable=True)
