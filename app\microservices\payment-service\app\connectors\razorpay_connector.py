import razorpay
from app.connectors.base import BaseConnector
from app.models.payment_request import PaymentRequest
from app.models.payment_response import PaymentResponse
from app.utils.logger import logger

class RazorpayConnector(BaseConnector):
    def initiate_payment(self, request: PaymentRequest) -> PaymentResponse:
        client = razorpay.Client(auth=(
            request.credentials.api_key.get_secret_value(),
            request.credentials.secret.get_secret_value()
        ))
        logger.info(f"Initiating Razorpay payment for order {request.order_id}")

        payment_data = {
            "amount": int(request.amount * 100),  # Razorpay expects amount in paise
            "currency": request.currency,
            "receipt": request.order_id,
            "notes": request.metadata
        }

        try:
            order = client.order.create(data=payment_data)
            return PaymentResponse(
                transaction_id=order["id"],
                payment_url=f"https://checkout.razorpay.com/v1/checkout.js?order_id={order['id']}",
                status=order["status"]
            )
        except Exception as e:
            logger.error(f"Razorpay payment initiation failed: {str(e)}")
            raise

    def verify_payment(self, request: PaymentRequest, transaction_id: str) -> dict:
        client = razorpay.Client(auth=(
            request.credentials.api_key.get_secret_value(),
            request.credentials.secret.get_secret_value()
        ))
        try:
            payment = client.payment.fetch(transaction_id)
            return payment
        except Exception as e:
            logger.error(f"Razorpay payment verification failed: {str(e)}")
            raise
