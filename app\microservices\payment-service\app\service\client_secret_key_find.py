import requests
from app.utils.logger import logger

def fetch_cashfree_credentials(client_id: str,provider: str) -> dict:
    try:
        # Internal API call to company-config-service
        response = requests.get(f"http://company-config-service:8000/api/v1/ms/gateway/provider/{client_id}/{provider}/credentials")
        response.raise_for_status()
        data = response.json()[0]
        logger.info(f"Fetched credentials for client_id: {client_id}")
        return data
    except requests.RequestException as e:
        logger.error(f"Failed to fetch credentials for client_id {client_id}: {str(e)}")
        raise Exception(f"Could not fetch Cashfree credentials from company-config-service. {str(e)}")