# routers/payment.py (updated with pagination and filters)
from datetime import date, datetime
import json
from fastapi import APIRouter, HTTPException, Request, Query
from app.models.payment_request import PaymentRequest
from app.models.payment_response import PaymentResponse
from app.service.payment_service import (
    initiate_payment, 
    handle_razorpay_webhook, 
    get_transactions_by_ref,
    update_booking, 
    verify_razorpay_signature,
    verify_cashfree_webhook,
    update_payment_status
)
from typing import List
from app.db.database import PaymentTransactionDB
from fastapi.responses import StreamingResponse
import io
import csv
from fastapi.responses import StreamingResponse
import io
import csv
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from fastapi import Form
from fastapi.responses import RedirectResponse
from app.utils.logger import logger
from app.service.client_secret_key_find import fetch_cashfree_credentials

router = APIRouter(prefix="/payment")

@router.post("/initiate", response_model=PaymentResponse)
def initiate_payment_route(r:Request,request: PaymentRequest):
    try:
        return initiate_payment(request)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/razorpay/webhook")
async def razorpay_webhook_listener(request: Request):
    try:
        body = await request.body()
        payload = await request.json()
        signature = request.headers.get("x-razorpay-signature")
        
        # Get credentials from webhook payload metadata
        notes = payload.get("payload", {}).get("payment", {}).get("entity", {}).get("notes", {})
        secret = notes.get("credentials", {}).get("secret")
        
        if not secret or not verify_razorpay_signature(body, signature, secret):
            raise HTTPException(status_code=403, detail="Invalid signature")

        payload = await request.json()
        handle_razorpay_webhook(payload)
        return {"status": "received"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/transactions", response_model=List[dict])
def get_transactions(
    booking_id: str = Query(None),
    wallet_id: str = Query(None),
    status: str = Query(None),
    limit: int = Query(10, gt=0, le=100),
    offset: int = Query(0, ge=0)
):
    if not booking_id and not wallet_id:
        raise HTTPException(status_code=400, detail="booking_id or wallet_id must be provided")
    return get_transactions_by_ref(booking_id=booking_id, wallet_id=wallet_id, status=status, limit=limit, offset=offset)



@router.get("/transactions/export")
def export_transactions(
    booking_id: str = Query(None),
    wallet_id: str = Query(None),
    status: str = Query(None)
):
    if not booking_id and not wallet_id:
        raise HTTPException(status_code=400, detail="booking_id or wallet_id must be provided")

    transactions = get_transactions_by_ref(booking_id=booking_id, wallet_id=wallet_id, status=status, limit=10000, offset=0)

    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=[
        "transaction_id", "booking_id", "wallet_id", "amount", "currency",
        "provider", "status", "created_at", "updated_at"
    ])
    writer.writeheader()
    for t in transactions:
        writer.writerow(t)

    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={
        "Content-Disposition": "attachment; filename=transactions_export.csv"
    })


@router.get("/transactions/export")
def export_transactions(
    booking_id: str = Query(None),
    wallet_id: str = Query(None),
    status: str = Query(None),
    format: str = Query("csv")
):
    if not booking_id and not wallet_id:
        raise HTTPException(status_code=400, detail="booking_id or wallet_id must be provided")

    transactions = get_transactions_by_ref(booking_id=booking_id, wallet_id=wallet_id, status=status, limit=10000, offset=0)

    if format == "csv":
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=[
            "transaction_id", "booking_id", "wallet_id", "amount", "currency",
            "provider", "status", "created_at", "updated_at"
        ])
        writer.writeheader()
        for t in transactions:
            writer.writerow(t)
        output.seek(0)
        return StreamingResponse(output, media_type="text/csv", headers={
            "Content-Disposition": "attachment; filename=transactions_export.csv"
        })

    elif format == "pdf":
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=A4)
        width, height = A4

        p.setFont("Helvetica-Bold", 12)
        p.drawString(50, height - 40, "Payment Transactions Export")

        y = height - 70
        p.setFont("Helvetica", 10)
        for t in transactions:
            line = f"{t['transaction_id']} | {t['booking_id']} | {t['amount']} {t['currency']} | {t['provider']} | {t['status']}"
            p.drawString(50, y, line)
            y -= 15
            if y < 40:
                p.showPage()
                y = height - 40

        p.save()
        buffer.seek(0)
        return StreamingResponse(buffer, media_type="application/pdf", headers={
            "Content-Disposition": "attachment; filename=transactions_export.pdf"
        })

    else:
        raise HTTPException(status_code=400, detail="Unsupported export format")


@router.post("/payu/callback")
def payu_callback(
    mihpayid: str = Form(...),
    txnid: str = Form(...),
    status: str = Form(...),
    hash: str = Form(...),
    additionalCharges: str = Form(None),
    **kwargs
):
    try:
        logger.info(f"PayU Callback: txnid={txnid}, status={status}, mihpayid={mihpayid}")

        # You can optionally verify hash here (skipped for brevity)
        # Then update the transaction status
        if status == "success":
            update_payment_status(txnid, txnid, "captured")
        elif status == "failure":
            update_payment_status(txnid, txnid, "failed")

        # Redirect to frontend success/failure page
        redirect_url = f"https://yourfrontend.com/payment/{status}"
        return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.error(f"PayU callback error: {str(e)}")
        raise HTTPException(status_code=500, detail="Callback processing failed")

@router.post("/cashfree/webhook")
async def cashfree_webhook_listener(request: Request):
    try:
        # Parse the incoming request
        body = await request.body()
        payload = await request.json()
        logger.info(f"Cashfree Webhook received: {payload}")

        # Extract necessary fields
        event_type = payload.get("type")
        data = payload.get("data", {})
        order = data.get("order", {})
        order_tags = order.get("order_tags", {})
        
        # Basic Validation
        if not event_type or not order.get("order_id"):
            raise HTTPException(status_code=400, detail="Invalid Payload Structure")

        company_id = order_tags.get("company_id")
        if not company_id:
            raise HTTPException(status_code=400, detail="Company ID missing from webhook")

        # Fetching credentials for verification
        # credentials = fetch_cashfree_credentials(company_id, 'cashfree')

        # if credentials.get('mappings'):
        #     signature = request.headers.get("x-cf-signature")
        #     secret = credentials['mappings'][0].get("secret")
        #     if not signature or not verify_cashfree_webhook(body, signature, secret):
        #         raise HTTPException(status_code=403, detail="Invalid signature")

        # Extract the required identifiers
        transaction_id = order.get("order_id")
        ref_id = (
            order_tags.get("booking_id") or
            order_tags.get("wallet_id") or
            transaction_id
        )

        logger.info(f"Payment successful for Transaction ID: {transaction_id} - {event_type}")
        # Handle Different Webhook Types
        if event_type == "PAYMENT_SUCCESS_WEBHOOK":
            logger.info(f"Payment successful for Transaction ID: {transaction_id}")
            update_payment_status(transaction_id, ref_id, "captured", payload)
            update_booking("captured", order_tags.get("booking_id"),date.today(), order.get('order_amount'), transaction_id, ref_id)
        elif event_type == "PAYMENT_FAILED_WEBHOOK":
            logger.info(f"Payment failed for Transaction ID: {transaction_id}")
            update_payment_status(transaction_id, ref_id, "failed", payload)
            update_booking("failed", order_tags.get("booking_id"),date.today(), order.get('order_amount'), transaction_id, ref_id)
        elif event_type == "PAYMENT_USER_DROPPED_WEBHOOK":
            logger.info(f"User dropped payment for Transaction ID: {transaction_id}")
            update_payment_status(transaction_id, ref_id, "user_dropped", payload)
            update_booking("user_dropped", order_tags.get("booking_id"),date.today(), order.get('order_amount'), transaction_id, ref_id)
        else:
            logger.warning(f"Unhandled event type: {event_type}")
            return {"status": "unhandled_event"}

        return {"status": "received"}

    except HTTPException as http_ex:
        logger.error(f"HTTP Exception: {http_ex.detail}")
        raise http_ex

    except Exception as e:
        logger.error(f"Cashfree webhook processing failed: {str(e)}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")