FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Upgrade pip
RUN pip install --upgrade pip

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

COPY .env .
COPY alembic.ini .
# Verify key packages are installed
RUN python -c "import sqlalchemy; print(f'SQLAlchemy version: {sqlalchemy.__version__}')"
RUN python -c "import jwt; print(f'PyJWT version: {jwt.__version__}')" || echo "PyJWT not installed, but will be installed explicitly"

# Copy the rest of the application
COPY . .

# Ensure .env file is present (create if not)
RUN touch .env

# Create a simple health check endpoint if not present
RUN if [ ! -f "app.py" ]; then \
    echo 'from fastapi import FastAPI\n\
app = FastAPI()\n\
\n\
@app.get("/")\n\
async def root():\n\
    return {"message": "Authentication Service"}\n\
\n\
@app.get("/health")\n\
async def health():\n\
    return {"status": "ok"}\n\
' > app.py; \
fi

# Expose the port the app runs on
EXPOSE 8000

# Initialize alembic
RUN alembic init alembic || true

# Use uvicorn for running the app
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
