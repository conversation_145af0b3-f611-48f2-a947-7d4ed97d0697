# services/payment_service.py (updated with Razorpay HMAC verification)
import hmac
import hashlib
import json
from fastapi import Request, HTTPException
from pydantic import SecretStr
import requests
from app.db.database import SessionLocal, PaymentTransactionDB
from datetime import date, datetime
from app.models.payment_request import PaymentRequest
from app.connectors.factory import get_connector
from app.utils.logger import logger
from app.config.settings import PROVIDER_CREDENTIALS

# Save new payment transaction
def save_payment_transaction(request: PaymentRequest, transaction_id: str,response: dict, status: str = "initiated"):
    db = SessionLocal()
    try:
        request_data = request.json()
        new_payment = PaymentTransactionDB(
            transaction_id=transaction_id,
            booking_id=request.metadata.get("booking_id"),
            wallet_id=request.metadata.get("wallet_id"),
            amount=request.amount,
            currency=request.currency,
            provider=request.provider,
            transaction_type="payment",
            initiate_request=request_data,
            initiate_response=response.dict() if hasattr(response, 'dict') else response,
            status=status,
            request_data=request_data,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db.add(new_payment)
        db.commit()
        logger.info(f"Saved new payment transaction {transaction_id}")
    except Exception as e:
        logger.error(f"Failed to save payment transaction: {str(e)}")
    finally:
        db.close()

def update_payment_transaction(transaction_id: str, status: str, response_data: dict = None, error_data: dict = None):
    db = SessionLocal()
    try:
        transaction = db.query(PaymentTransactionDB).filter_by(transaction_id=transaction_id).first()
        if transaction:
            transaction.status = status
            transaction.updated_at = datetime.utcnow()
            if response_data:
                transaction.response_data = response_data
            if error_data:
                transaction.error_data = error_data
            db.commit()
            logger.info(f"Updated transaction {transaction_id} with status {status}")
    except Exception as e:
        logger.error(f"Failed to update transaction {transaction_id}: {str(e)}")
    finally:
        db.close()

def initiate_payment(request: PaymentRequest):
    connector_class = get_connector(request.provider)
    connector = connector_class()
    response = connector.initiate_payment(request)
    save_payment_transaction(request, response.transaction_id,response)
    if hasattr(response, 'dict'):
        update_payment_transaction(
            transaction_id=response.transaction_id,
            status="processing",
            response_data=response.dict()
        )
    return response


def verify_razorpay_signature(body: bytes, received_signature: str, secret: str):
    generated_signature = hmac.new(secret.encode(), body, hashlib.sha256).hexdigest()
    return hmac.compare_digest(generated_signature, received_signature)


def handle_razorpay_webhook(payload: dict):
    event = payload.get("event")
    entity = payload.get("payload", {}).get("payment", {}).get("entity", {})
    transaction_id = entity.get("order_id")
    status = entity.get("status")
    notes = entity.get("notes", {})

    logger.info(f"Webhook received: event={event}, transaction_id={transaction_id}, status={status}")

    booking_id = notes.get("booking_id")
    wallet_id = notes.get("wallet_id")

    if not transaction_id or (not booking_id and not wallet_id):
        logger.warning("Missing identifiers in webhook payload")
        return

    if event == "payment.captured":
        logger.info("Payment captured. Processing...")
        update_payment_transaction(
            transaction_id=transaction_id,
            status="captured",
            response_data=entity
        )
    elif event == "payment.failed":
        error_details = {
            "error_code": entity.get("error_code"),
            "error_description": entity.get("error_description"),
            "error_reason": entity.get("error_reason")
        }
        logger.warning(f"Payment failed. Error details: {error_details}")
        update_payment_transaction(
            transaction_id=transaction_id,
            status="failed",
            error_data=error_details
        )
    else:
        logger.info(f"Unhandled event type: {event}")



def verify_cashfree_webhook(body: bytes, signature: str, secret: str):
    """Verify Cashfree webhook signature using SHA256 algorithm"""
    generated_signature = hmac.new(secret.encode(), body, hashlib.sha256).hexdigest()
    return hmac.compare_digest(generated_signature, signature)

def update_payment_status(transaction_id: str, ref_id: str, status: str,resp :str):
    """Update payment status from webhook events"""
    db = SessionLocal()
    try:
        transaction = db.query(PaymentTransactionDB).filter_by(transaction_id=transaction_id).first()
        if transaction:
            transaction.status = status
            transaction.response_data = resp
            transaction.updated_at = datetime.utcnow()
            transaction.webhook_request = resp
            # Update reference ID if not already set
            if not transaction.booking_id and not transaction.wallet_id:
                if ref_id.startswith("BOOK"):
                    transaction.booking_id = ref_id
                else:
                    transaction.wallet_id = ref_id
            
            db.commit()
            logger.info(f"Updated transaction {transaction_id} status to {status}")
    except Exception as e:
        logger.error(f"Failed to update transaction status: {str(e)}")
    finally:
        db.close()

def get_transactions_by_ref(booking_id=None, wallet_id=None, status=None, limit=10, offset=0):
    db = SessionLocal()
    try:
        query = db.query(PaymentTransactionDB)
        if booking_id:
            query = query.filter_by(booking_id=booking_id)
        if wallet_id:
            query = query.filter_by(wallet_id=wallet_id)
        if status:
            query = query.filter_by(status=status)

        transactions = query.order_by(PaymentTransactionDB.created_at.desc()).offset(offset).limit(limit).all()
        return [
            {
                "transaction_id": t.transaction_id,
                "booking_id": t.booking_id,
                "wallet_id": t.wallet_id,
                "amount": t.amount,
                "currency": t.currency,
                "provider": t.provider,
                "transaction_type": t.transaction_type,
                "status": t.status,
                "request_data": t.request_data,
                "response_data": t.response_data,
                "error_data": t.error_data,
                "created_at": t.created_at.isoformat(),
                "updated_at": t.updated_at.isoformat()
            } for t in transactions
        ]
    finally:
        db.close()


def update_booking( payment_status: str = 'initiated', booking_id: str =  None, paid_on: date = None, paid_amount: float = None, transaction_ref: str = None, payment_reference_id: str = None):

    response = requests.post(f"http://booking-service:8000/api/v1/ms/payment/update/booking",json={
        "payment_status": payment_status,
        "booking_id": booking_id,
        "paid_on": str(paid_on), 
        "paid_amount": paid_amount,
        "transaction_ref": transaction_ref,
        "payment_reference_id": payment_reference_id
    })
    
    if response.status_code == 200:
       logger.info("Booking Updated")
       return True
    else:
        logger.error("Couldn't update booking")
        raise HTTPException(status_code=403, detail=f"Couldn't update booking ")