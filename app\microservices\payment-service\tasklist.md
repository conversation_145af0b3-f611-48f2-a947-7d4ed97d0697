# Payment Connector Service Task List

## ✅ Completed Tasks

### 1. Project Setup
- [x] FastAPI project initialized
- [x] Project structure defined with `routers`, `services`, `models`, `connectors`, `config`, `utils`

### 2. Razorpay Integration
- [x] Razorpay connector implemented with:
  - Initiate payment
  - Verify payment
- [x] Dynamic connector loading via `factory.py`
- [x] Razorpay webhook handling
- [x] Metadata support for `booking_id` / `wallet_id`

### 3. Database Integration
- [x] SQLAlchemy-based DB model (`PaymentTransactionDB`)
- [x] SQLite for dev (configurable to PostgreSQL/MySQL)
- [x] Insert transaction on payment initiation
- [x] Update transaction status on webhook

### 4. Query & Reporting
- [x] Retrieve transactions by `booking_id` / `wallet_id`
- [x] Filters: `status`, `limit`, `offset`
- [x] CSV export endpoint
- [x] PDF export with pagination

---

## 🛠️ Pending Tasks (Next Steps)

### 🔐 1. Security & Validation
- [ ] Add HMAC verification for Razorpay webhooks
- [ ] Add API key or OAuth-based authentication for routes
- [ ] Input validation and error handling improvements

### 🧩 2. Additional Gateway Support
- [ ] Implement `StripeConnector`
- [ ] Implement `PayPalConnector`
- [ ] Add dynamic company-to-gateway mapping (via DB or config service)

### 🗄️ 3. Admin & Audit
- [ ] Create an admin dashboard for viewing & managing transactions
- [ ] Add audit logs for all payment state changes
- [ ] Track retry history, IP logs, webhook attempts

### 🔄 4. Async & Performance
- [ ] Make webhook and DB writes async with FastAPI
- [ ] Use Redis/Celery for retrying failed webhook tasks or payment status polling

### 🔔 5. Notifications (Optional)
- [ ] Trigger email/WhatsApp/SMS on payment status change
- [ ] Integrate with a centralized notification microservice (if available)

### 📦 6. Deployment Readiness
- [ ] Create Dockerfile and docker-compose.yml
- [ ] Add env-based configuration management
- [ ] Unit and integration tests for payment flows

### 📚 7. Documentation
- [ ] Auto-generate OpenAPI docs via FastAPI
- [ ] Write API usage guide for client systems
- [ ] Include example payloads, expected responses, error formats
