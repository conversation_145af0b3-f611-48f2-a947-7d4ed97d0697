# Code Quality Standards

## Database Code
- Use SQLAlchemy ORM patterns consistently
- All models must inherit from Base
- Explicit column definitions (no implicit types)
- Proper nullability constraints
- Document relationships in docstrings
- Use session context managers for transactions

## Migrations (Alembic)
- Single migration per logical change
- Include both upgrade and downgrade methods
- Test migrations in staging before production
- Document migration purpose in message
- Never modify existing migrations

## Settings Management
- Use Pydantic BaseSettings for all config
- Environment variables for sensitive data
- Type hints for all settings
- Validation for critical values
- Group related settings in nested models
