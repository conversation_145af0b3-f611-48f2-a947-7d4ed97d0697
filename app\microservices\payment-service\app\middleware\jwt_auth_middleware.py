from fastapi import Request, HTTPException
from fastapi.responses import JSO<PERSON>esponse
from jose import jwt, <PERSON><PERSON><PERSON><PERSON><PERSON>, ExpiredSignatureError, J<PERSON><PERSON>rror
from starlette.middleware.base import BaseHTTPMiddleware
from app.config.settings import app_settings
import traceback

PUBLIC_ROUTES = ["/api/v1/payment/cashfree/webhook"]

class JWTValidationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Check if the path is public
        print(request.url.path)
        if request.url.path in PUBLIC_ROUTES:
            response = await call_next(request)
            return response
            
        # If not public, validate the JWT
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=401,
                content={"detail": "Authorization header missing or malformed"}
            )
            
        token = auth_header.split(" ")[1]
        
        # Validate token format
        if not token or token.count('.') != 2:
            return JSONResponse(
                status_code=401,
                content={"detail": "Malformed token"}
            )
            
        try:
            payload = jwt.decode(token, app_settings.JWT_SECRET, algorithms=[app_settings.JWT_ALGORITHM])
            request.state.user_id = payload.get("user_id")
            request.state.email = payload.get("email")
            request.state.company_id = payload.get("company_id")
            request.state.is_superadmin = payload.get("is_superadmin", False)
        except ExpiredSignatureError:
            # Handle expired token specifically
            return JSONResponse(
                status_code=401,
                content={"detail": "Token has expired"}
            )
        except ValueError:
            # Handle value errors (like unpacking issues)
            return JSONResponse(
                status_code=401,
                content={"detail": "Invalid token format"}
            )
        except JWSError:
            # Handle JWS specific errors
            return JSONResponse(
                status_code=401,
                content={"detail": "Invalid token structure"}
            )
        except JWTError:
            # Handle other JWT validation errors
            return JSONResponse(
                status_code=403,
                content={"detail": "Invalid JWT Token"}
            )
        except Exception as e:
            # Catch any other unexpected errors
            print(f"Unexpected error in JWT middleware: {str(e)}")
            print(traceback.format_exc())
            return JSONResponse(
                status_code=500,
                content={"detail": "Authentication error"}
            )
            
        # If all authentication checks pass, proceed with the request
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Catch any exceptions from the route handlers
            print(f"Error in route handler: {str(e)}")
            print(traceback.format_exc())
            return JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )