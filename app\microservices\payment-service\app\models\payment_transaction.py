from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

class TransactionType(str, Enum):
    PAYMENT = 'payment'
    REFUND = 'refund'
    SETTLEMENT = 'settlement'

class PaymentTransaction(BaseModel):
    transaction_id: str
    booking_id: Optional[str] = None
    wallet_id: Optional[str] = None
    amount: float
    currency: str
    provider: str
    transaction_type: TransactionType = TransactionType.PAYMENT
    status: str  # e.g., 'initiated', 'captured', 'failed'
    request_data: Dict[str, Any]
    response_data: Optional[Dict[str, Any]] = None
    error_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
