# Product Context

## Project Purpose
Payment processing service for handling transactions with multiple payment providers.

## Key Features
- Payment transaction processing
- Multiple payment provider integrations (PayU, Cashfree, Razorpay)
- Transaction status tracking
- Database persistence for transactions

## Current Database
- Using SQLite (test.db)
- SQLAlchemy ORM
- Single table for payment transactions
- Basic CRUD operations

## To Be Confirmed
- Expected transaction volume
- Performance requirements
- High availability needs
- Backup requirements
