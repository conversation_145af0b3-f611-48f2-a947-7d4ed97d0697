import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from functools import wraps
from app.config.settings import app_settings

def requires_role(role_name):
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            user_id = request.state.user_id
            if not user_id:
                raise HTTPException(status_code=401, detail="User is not authenticated")

            response = requests.post(f"{app_settings.AUTH_SERVICE_URL}/validate-role", json={
                "user_id": user_id,
                "role_name": role_name
            })

            if response.status_code == 200 and response.json().get("valid"):
                return await func(request, *args, **kwargs)
            else:
                raise HTTPException(status_code=403, detail=f"User does not have the role '{role_name}'")
        return wrapper
    return decorator

def requires_permission(permission_name):
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            user_id = request.state.user_id
            if not user_id:
                raise HTTPException(status_code=401, detail="User is not authenticated")

            response = requests.post(f"{app_settings.AUTH_SERVICE_URL}/validate-permission", json={
                "user_id": user_id,
                "permission_name": permission_name
            })

            if response.status_code == 200 and response.json().get("valid"):
                return await func(request, *args, **kwargs)
            else:
                raise HTTPException(status_code=403, detail=f"User does not have the permission '{permission_name}'")
        return wrapper
    return decorator