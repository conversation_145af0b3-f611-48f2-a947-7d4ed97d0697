from pydantic_settings import BaseSettings
from typing import Dict, Any

class DatabaseSettings(BaseSettings):
    url: str = "mysql+pymysql://root:Vishnu123@localhost:3306/tdb_payment"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    echo: bool = False

    class Config:
        env_prefix = "DB_"

class AppSettings(BaseSettings):
    debug: bool = False
    environment: str = "development"
    log_level: str = "INFO"
    AUTH_SERVICE_URL:str = "http://authentication-service:8000"
    JWT_SECRET:str = "37lyRjQG_8NqXwzTpKsVdYb2Wf5H6Jm9"
    JWT_ALGORITHM:str = "HS256"

    class Config:
        env_prefix = "APP_"

class ProviderCredentials(BaseSettings):
    stripe: Dict[str, str] = {"api_key": "sk_test_..."}
    razorpay: Dict[str, str] = {"api_key": "rzp_test_...", "secret": "..."}
    paypal: Dict[str, str] = {"client_id": "...", "secret": "..."}
    payu: Dict[str, str] = {
        "key": "your_merchant_key",
        "salt": "your_salt",
        "base_url": "https://test.payu.in"
    }
    cashfree: Dict[str, str] = {
        "app_id": "your_cashfree_app_id",
        "secret_key": "your_cashfree_secret_key",
        "env": "test"
    }

    class Config:
        env_prefix = "PROVIDER_"

# Initialize settings
db_settings = DatabaseSettings()
app_settings = AppSettings()
provider_credentials = ProviderCredentials()

# Backward compatibility
PROVIDER_CREDENTIALS = provider_credentials.model_dump()
