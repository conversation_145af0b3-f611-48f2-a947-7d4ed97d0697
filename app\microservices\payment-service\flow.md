# Payment Connector Service Documentation

## Overview
The Payment Connector Service is a microservice designed to handle multi-gateway, multi-company payment processing. It supports dynamic routing to different payment providers (like Razorpay, Stripe, and PayPal), handles transaction tracking, provides webhook support, and allows transaction export and status verification.

## Core Features
- Support for multiple payment providers via modular connectors
- Initiating and verifying payments
- Webhook event handling
- Transaction persistence (linked to `booking_id` or `wallet_id`)
- Querying and exporting transaction records

## Technologies Used
- **FastAPI** for API layer
- **SQLAlchemy** for ORM and database access
- **Razorpay SDK** for payment gateway integration
- **SQLite** for development DB (switchable to PostgreSQL/MySQL)
- **ReportLab** and **csv** for export features

## Service Flow

### 1. Initiating a Payment
**Endpoint:** `POST /payment/initiate`

#### Flow:
1. Client (e.g., booking service) sends a payment initiation request with metadata (`booking_id` or `wallet_id`).
2. The factory selects the correct connector based on the `provider` field.
3. The connector (e.g., RazorpayConnector) creates a payment order.
4. Response includes transaction ID and payment URL.
5. Transaction is saved in the database with status `initiated`.

### 2. Webhook Handling
**Endpoint:** `POST /payment/razorpay/webhook`

#### Flow:
1. Razorpay sends a webhook on payment status change.
2. Payload includes the `order_id` and optional metadata (`notes`) containing `booking_id` or `wallet_id`.
3. The service updates the transaction status in the DB (`captured`, `failed`, etc.).

### 3. Transaction Query
**Endpoint:** `GET /payment/transactions`

#### Parameters:
- `booking_id` or `wallet_id` (required)
- `status` (optional)
- `limit`, `offset` (for pagination)

#### Flow:
1. The service fetches transactions matching the filters.
2. Returns JSON list of transaction data.

### 4. Transaction Export
**Endpoint:** `GET /payment/transactions/export`

#### Parameters:
- `booking_id` or `wallet_id` (required)
- `status` (optional)
- `format`: `csv` or `pdf`

#### Flow:
1. The service fetches and formats transactions.
2. Returns downloadable CSV or PDF.

## Project Structure
```
payment_service/
├── main.py                    # FastAPI app
├── routers/
│   └── payment.py            # API routes
├── connectors/               # Gateway connectors
│   ├── base.py
│   ├── razorpay_connector.py
│   ├── factory.py
├── models/                   # Request/response schemas
│   ├── payment_request.py
│   ├── payment_response.py
├── services/
│   └── payment_service.py    # Core payment logic
├── db/
│   └── database.py           # SQLAlchemy DB setup
├── utils/
│   ├── logger.py             # Logger config
│   └── exceptions.py         # Custom errors
├── config/
│   └── settings.py           # API keys and secrets
```

## Example Usage
```json
POST /payment/initiate
{
  "company_id": "ecogo",
  "amount": 1500.0,
  "currency": "INR",
  "order_id": "ORD123",
  "provider": "razorpay",
  "metadata": {
    "booking_id": "BOOK123"
  }
}
```

## Next Steps for Production
- Add security: HMAC checks, auth
- Add Stripe/PayPal connectors
- Implement dashboard UI
- Add tests and CI/CD

---
This service allows seamless scaling and plugging of new gateways, tailored for multi-tenant B2B environments.
