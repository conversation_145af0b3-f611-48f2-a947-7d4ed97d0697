# Technical Context

## Current Technologies
- Python
- SQLAlchemy (with SQLite)
- FastAPI (inferred from file structure)
- Multiple payment provider SDKs

## Development Setup
- Docker-based environment
- SQLite database file
- Environment variables in .env
- Basic requirements.txt dependencies

## Planned Additions
### PostgreSQL
- Production-grade RDBMS
- psycopg2 driver
- Connection pooling
- Performance optimizations

### Alembic
- Database migration tool
- Version control for schema changes
- Support for rollbacks
- Environment-aware migrations

### Pydantic Settings
- Type-safe configuration
- Environment variable parsing
- Nested settings structure
- Validation capabilities
