{"info": {"name": "Payment Connector Service", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "item": [{"name": "Initiate Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"company_id\": \"test_company\",\n  \"amount\": 1000.0,\n  \"currency\": \"INR\",\n  \"order_id\": \"ORD123456\",\n  \"provider\": \"razorpay\",\n  \"credentials\": {\n    \"api_key\": \"rzp_test_YOUR_API_KEY\",\n    \"secret\": \"YOUR_SECRET_KEY\"\n  },\n  \"metadata\": {\n    \"booking_id\": \"BOOK123\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"**********\",\n    \"productinfo\": \"Flight Booking\"\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/initiate", "host": ["{{base_url}}"], "path": ["payment", "initiate"]}}}, {"name": "Get Transactions", "request": {"method": "GET", "url": {"raw": "{{base_url}}/payment/transactions?booking_id=BOOK123", "host": ["{{base_url}}"], "path": ["payment", "transactions"], "query": [{"key": "booking_id", "value": "BOOK123"}]}}}, {"name": "Export Transactions (CSV)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/payment/transactions/export?booking_id=BOOK123&format=csv", "host": ["{{base_url}}"], "path": ["payment", "transactions", "export"], "query": [{"key": "booking_id", "value": "BOOK123"}, {"key": "format", "value": "csv"}]}}}, {"name": "Export Transactions (PDF)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/payment/transactions/export?booking_id=BOOK123&format=pdf", "host": ["{{base_url}}"], "path": ["payment", "transactions", "export"], "query": [{"key": "booking_id", "value": "BOOK123"}, {"key": "format", "value": "pdf"}]}}}, {"name": "Ra<PERSON><PERSON>y <PERSON>hook (Simulated)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-razorpay-signature", "value": "test_signature"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment.captured\",\n  \"payload\": {\n    \"payment\": {\n      \"entity\": {\n        \"id\": \"pay_ABC123\",\n        \"order_id\": \"ORD123456\",\n        \"status\": \"captured\",\n        \"notes\": {\n          \"booking_id\": \"BOOK123\",\n          \"credentials\": {\n            \"secret\": \"YOUR_SECRET_KEY\"\n          }\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/razorpay/webhook", "host": ["{{base_url}}"], "path": ["payment", "razorpay", "webhook"]}}}, {"name": "Cashfree Webhook (Simulated)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"ORD123456\",\n  \"order_status\": \"PAID\",\n  \"order_tags\": {\n    \"booking_id\": \"BOOK123\"\n  }\n}"}, "url": {"raw": "{{base_url}}/payment/cashfree/webhook", "host": ["{{base_url}}"], "path": ["payment", "cashfree", "webhook"]}}}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}]}