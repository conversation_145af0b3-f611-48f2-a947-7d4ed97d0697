import requests
from app.connectors.base import BaseConnector
from app.models.payment_request import PaymentRequest
from app.models.payment_response import PaymentResponse
from app.utils.logger import logger
from app.service.client_secret_key_find import fetch_cashfree_credentials

class CashfreeConnector(BaseConnector):
    def initiate_payment(self, request: PaymentRequest) -> PaymentResponse:
        env = request.metadata.get("env", "test")
        base_url = "https://sandbox.cashfree.com/pg" if env == "test" else "https://api.cashfree.com/pg"
        logger.info(f"Initiating Cashfree payment for order {request.order_id}")

        payload = {
            "order_id": request.order_id,
            "order_amount": request.amount,
            "order_currency": request.currency,
            "customer_details": {
                "customer_id": request.metadata.get("customer_id", request.order_id),
                "customer_email": request.metadata.get("email", "<EMAIL>"),
                "customer_phone": request.metadata.get("phone", "9999999999")
            },
            "order_meta": {
                "return_url": request.metadata.get("return_url", "https://example.com/cashfree/callback")
            },
            "order_tags":{
                "company_id":request.company_id,
                "booking_id":request.metadata.get("booking_id","")
            },
            "metadata": request.metadata
        }

        headers = {
            "Content-Type": "application/json",
            "x-api-version": "2022-09-01",
            "x-client-id": request.credentials.api_key.get_secret_value(),
            "x-client-secret": request.credentials.secret.get_secret_value()
        }

        try:
            response = requests.post(
                f"{base_url}/orders",
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            data = response.json()
            payment_url = data.get("payment_link") or data["payments"]["url"]
            return PaymentResponse(
                transaction_id=data["order_id"],
                payment_url=payment_url,
                status="initiated",
                other=data
            )
        except Exception as e:
            logger.error(f"Cashfree initiation failed: {str(e)}")
            raise

    def verify_payment(self, request: PaymentRequest, transaction_id: str) -> dict:
        env = request.metadata.get("env", "test")
        base_url = "https://sandbox.cashfree.com/pg" if env == "test" else "https://api.cashfree.com/pg"
        headers = {
            "Content-Type": "application/json",
            "x-api-version": "2022-09-01",
            "x-client-id": request.credentials.api_key.get_secret_value(),
            "x-client-secret": request.credentials.secret.get_secret_value()
        }
        try:
            response = requests.get(f"{base_url}/orders/{transaction_id}", headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Cashfree verification failed: {str(e)}")
            return {"status": "error", "message": str(e)}
