# connectors/payu_connector.py
import hashlib
import requests
from app.connectors.base import BaseConnector
from app.models.payment_request import PaymentRequest
from app.models.payment_response import PaymentResponse
from app.utils.logger import logger

class PayUConnector(BaseConnector):
    def generate_hash(self, key: str, salt: str, txnid: str, amount: str, productinfo: str, firstname: str, email: str):
        hash_string = f"{key}|{txnid}|{amount}|{productinfo}|{firstname}|{email}|||||||||||{salt}"
        return hashlib.sha512(hash_string.encode()).hexdigest()

    def initiate_payment(self, request: PaymentRequest) -> PaymentResponse:
        logger.info(f"Initiating PayU payment for order {request.order_id}")

        # NOTE: Modify these as needed for actual user metadata
        firstname = request.metadata.get("firstname", "Test")
        email = request.metadata.get("email", "<EMAIL>")
        productinfo = request.metadata.get("productinfo", "Booking")
        base_url = request.metadata.get("base_url", "https://test.payu.in")

        txnid = request.order_id
        amount = str(request.amount)
        key = request.credentials.api_key.get_secret_value()
        salt = request.credentials.secret.get_secret_value()
        hash_value = self.generate_hash(key, salt, txnid, amount, productinfo, firstname, email)

        payload = {
            "key": key,
            "txnid": txnid,
            "amount": amount,
            "productinfo": productinfo,
            "firstname": firstname,
            "email": email,
            "phone": request.metadata.get("phone", "**********"),
            "surl": request.metadata.get("surl", "https://example.com/success"),
            "furl": request.metadata.get("furl", "https://example.com/failure"),
            "hash": hash_value,
            "service_provider": "payu_paisa"
        }

        # In PayU, redirection happens via POST to /_payment with form data
        payment_url = f"{base_url}/_payment"

        return PaymentResponse(
            transaction_id=txnid,
            payment_url=payment_url,
            status="initiated"
        )

    def verify_payment(self, request: PaymentRequest, transaction_id: str) -> dict:
        logger.info("Manual verification via PayU dashboard or callback.")
        return {"status": "pending", "message": "Verify via PayU callback or dashboard"}
