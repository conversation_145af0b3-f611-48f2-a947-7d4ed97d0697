import os
from sqlalchemy import create_engine, Column, String, Float, DateTime, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool
import datetime
from app.config.settings import db_settings

engine = create_engine(
    db_settings.url,
    poolclass=QueuePool,
    pool_size=db_settings.pool_size,
    max_overflow=db_settings.max_overflow,
    pool_timeout=db_settings.pool_timeout,
    pool_pre_ping=True,
    echo=db_settings.echo
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class PaymentTransactionDB(Base):
    __tablename__ = "payment_transactions"

    transaction_id = Column(String(length=100), primary_key=True, index=True)
    booking_id = Column(String(length=20), index=True, nullable=True)
    wallet_id = Column(String(length=20), index=True, nullable=True)
    amount = Column(Float)
    currency = Column(String(length=20))
    provider = Column(String(length=20))
    transaction_type = Column(String(length=20), default="payment")
    status = Column(String(length=20))
    initiate_request = Column(Text, nullable=True)  # Store JSON as TEXT
    initiate_response = Column(Text, nullable=True)  # Store JSON as TEXT
    webhook_request = Column(Text, nullable=True)  # Store JSON as TEXT
    request_data = Column(Text)  # Store JSON as TEXT
    response_data = Column(Text, nullable=True)  # Store JSON as TEXT
    error_data = Column(Text, nullable=True)  # Store JSON as TEXT
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

Base.metadata.create_all(bind=engine)
