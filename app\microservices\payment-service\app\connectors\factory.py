from app.connectors.cashfree_connector import CashfreeConnector
from app.connectors.razorpay_connector import RazorpayConnector
from app.connectors.payu_connector import PayUConnector

def get_connector(provider: str):
    if provider == "razorpay":
        return RazorpayConnector
    elif provider == "payu":
        return PayUConnector
    elif provider == "cashfree":
        return CashfreeConnector
    else:
        raise ValueError("Unsupported provider")
